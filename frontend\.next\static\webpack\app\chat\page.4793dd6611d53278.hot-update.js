"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ChatInput.tsx":
/*!***********************************************!*\
  !*** ./src/app/chat/components/ChatInput.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInput: () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _app_chat_components_ToolSettings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/chat/components/ToolSettings */ \"(app-pages-browser)/./src/app/chat/components/ToolSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInput auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatInput(param) {\n    let { inputMessage, isStreaming, selectedModel, enableTools, selectedTools, onInputChange, onSendMessage, onStopGeneration, onKeyPress, onToolsToggle, onSelectedToolsChange, onClearChat } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动调整textarea高度\n    const adjustHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            textarea.style.height = 'auto';\n            const scrollHeight = textarea.scrollHeight;\n            const lineHeight = 24; // 大约每行24px\n            const maxHeight = lineHeight * 6; // 最大6行\n            const minHeight = lineHeight * 1; // 最小1行\n            const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);\n            textarea.style.height = \"\".concat(newHeight, \"px\");\n            // 如果内容超过最大高度，启用滚动\n            if (scrollHeight > maxHeight) {\n                textarea.style.overflowY = 'auto';\n            } else {\n                textarea.style.overflowY = 'hidden';\n            }\n        }\n    };\n    // 当输入内容变化时调整高度\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            adjustHeight();\n        }\n    }[\"ChatInput.useEffect\"], [\n        inputMessage\n    ]);\n    // 当输入框被清空时（发送消息后）自动聚焦\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            if (inputMessage === '' && textareaRef.current) {\n                // 使用setTimeout确保DOM更新完成后再聚焦\n                setTimeout({\n                    \"ChatInput.useEffect\": ()=>{\n                        var _textareaRef_current;\n                        (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                    }\n                }[\"ChatInput.useEffect\"], 50);\n            }\n        }\n    }[\"ChatInput.useEffect\"], [\n        inputMessage\n    ]);\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        onInputChange(e.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_ToolSettings__WEBPACK_IMPORTED_MODULE_2__.ToolSettings, {\n                selectedModel: selectedModel,\n                enableTools: enableTools,\n                selectedTools: selectedTools,\n                onToolsToggle: onToolsToggle,\n                onSelectedToolsChange: onSelectedToolsChange,\n                onClearChat: onClearChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatInput.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-3 items-end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        ref: textareaRef,\n                        value: inputMessage,\n                        onChange: handleInputChange,\n                        onKeyPress: onKeyPress,\n                        placeholder: \"输入消息...\",\n                        className: \"flex-1 p-3 border border-theme-input-border rounded-lg resize-none bg-theme-input text-theme-foreground placeholder-theme-foreground-muted focus:ring-2 focus:ring-theme-input-focus focus:border-transparent transition-colors duration-200\",\n                        style: {\n                            minHeight: '24px',\n                            lineHeight: '24px'\n                        },\n                        rows: 1\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatInput.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: isStreaming ? onStopGeneration : onSendMessage,\n                        disabled: !isStreaming && !inputMessage.trim(),\n                        className: \"relative w-12 h-12 text-white rounded-full flex items-center justify-center transition-all duration-200 \".concat(isStreaming ? 'bg-theme-error hover:bg-red-700' : 'bg-theme-primary hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed', \" \").concat(isStreaming ? 'before:content-[\\\"\\\"] before:absolute before:inset-0 before:rounded-full before:border-2 before:border-transparent before:border-t-white before:border-r-white before:animate-spin' : ''),\n                        children: isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4 relative z-10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatInput.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatInput.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatInput.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatInput.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ChatInput, \"9N7qFGUrBoc4yXHMJAYm4PKHw3I=\");\n_c = ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ChatInput.tsx\n"));

/***/ })

});