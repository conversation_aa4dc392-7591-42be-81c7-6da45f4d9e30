{"/api/conversations/route": "app/api/conversations/route.js", "/api/chat/route": "app/api/chat/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/models/route": "app/api/models/route.js", "/api/conversations/[id]/route": "app/api/conversations/[id]/route.js", "/api/mcp/tools/route": "app/api/mcp/tools/route.js", "/api/mcp/server-list/route": "app/api/mcp/server-list/route.js", "/chat/page": "app/chat/page.js", "/mcp-config/page": "app/mcp-config/page.js", "/page": "app/page.js"}