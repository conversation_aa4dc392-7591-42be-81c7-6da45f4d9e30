"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx":
/*!**************************************************!*\
  !*** ./src/app/chat/components/ThinkingMode.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThinkingMode: () => (/* binding */ ThinkingMode),\n/* harmony export */   hasThinkingContent: () => (/* binding */ hasThinkingContent),\n/* harmony export */   removeThinkingContent: () => (/* binding */ removeThinkingContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* __next_internal_client_entry_do_not_use__ ThinkingMode,hasThinkingContent,removeThinkingContent auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ThinkingMode(param) {\n    let { content, isExpanded, onToggleExpand, defaultHidden = false } = param;\n    _s();\n    const [initiallyHidden, setInitiallyHidden] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultHidden);\n    const [thinkingContent, setThinkingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 提取<think></think>标签内的内容，支持流式渲染\n    const extractThinkingContent = (text)=>{\n        const thinkRegex = /<think>([\\s\\S]*?)(?:<\\/think>|$)/g;\n        const matches = text.match(thinkRegex);\n        if (!matches) return '';\n        return matches.map((match)=>{\n            return match.replace(/<\\/?think>/g, '');\n        }).join('\\n\\n');\n    };\n    // 检测思考状态变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThinkingMode.useEffect\": ()=>{\n            const newExtracted = extractThinkingContent(content);\n            setThinkingContent(newExtracted);\n            const hasThinkStart = /<think>/.test(content);\n            // 如果有思考内容或正在思考，确保组件可见\n            if (newExtracted || hasThinkStart) {\n                if (initiallyHidden) {\n                    setInitiallyHidden(false);\n                    console.log('🔍 检测到思考内容，显示思考面板');\n                }\n            }\n        }\n    }[\"ThinkingMode.useEffect\"], [\n        content,\n        initiallyHidden\n    ]);\n    // 如果初始隐藏，且没有思考内容，则不渲染\n    if (initiallyHidden && !thinkingContent) {\n        return null;\n    }\n    // 检测是否正在思考中（有开始标签但没有结束标签）\n    const isCurrentlyThinking = /<think>/.test(content) && !/<\\/think>/.test(content);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-purple-200 dark:border-purple-700 rounded-lg bg-purple-50 dark:bg-purple-900/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onToggleExpand,\n                className: \"flex items-center justify-between p-3 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-800/20 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-purple-800 dark:text-purple-200\",\n                                children: \"思考模式\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: isCurrentlyThinking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600 dark:text-purple-400 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-green-600 dark:text-green-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-purple-200 dark:border-purple-700 p-3\",\n                children: [\n                    isCurrentlyThinking && !thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this),\n                    thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-purple-700 dark:text-purple-300 whitespace-pre-wrap bg-white dark:bg-gray-800 dark:border-purple-600\",\n                        children: thinkingContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(ThinkingMode, \"Sag9ybofXahqIKKGqBsCIUomYeQ=\");\n_c = ThinkingMode;\n// 导出辅助函数供其他组件使用\nconst hasThinkingContent = (content)=>{\n    return /<think>/.test(content);\n};\nconst removeThinkingContent = (content)=>{\n    return content.replace(/<think>[\\s\\S]*?(?:<\\/think>|$)/g, '').trim();\n};\nvar _c;\n$RefreshReg$(_c, \"ThinkingMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx\n"));

/***/ })

});