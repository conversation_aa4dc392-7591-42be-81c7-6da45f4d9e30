"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/Sidebar.tsx":
/*!*****************************!*\
  !*** ./src/app/Sidebar.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nfunction Sidebar(param) {\n    let { models, conversations, currentConversation, selectedModel, showModelSelector, onModelChange, onShowModelSelector, onCreateConversation, onLoadConversation, onDeleteConversation } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-theme-card border-r border-theme-border flex flex-col transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-theme-foreground\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                        variant: \"icon\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onShowModelSelector(!showModelSelector),\n                                        className: \"p-2 text-theme-foreground-muted hover:text-theme-foreground transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    showModelSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-theme-background-tertiary rounded-lg transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-theme-foreground-secondary mb-2\",\n                                children: \"选择模型\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedModel,\n                                onChange: (e)=>onModelChange(e.target.value),\n                                className: \"w-full p-2 border border-theme-input-border rounded-md bg-theme-input text-theme-foreground focus:border-theme-input-focus focus:ring-1 focus:ring-theme-input-focus transition-colors duration-200\",\n                                children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: model.name,\n                                        children: [\n                                            model.name,\n                                            \" (\",\n                                            (model.size / 1024 / 1024 / 1024).toFixed(1),\n                                            \"GB)\"\n                                        ]\n                                    }, model.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCreateConversation,\n                        disabled: !selectedModel,\n                        className: \"w-full flex items-center justify-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed mb-2 transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            \"新建对话\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/mcp-config\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center gap-2 px-4 py-2 bg-theme-success text-white rounded-lg hover:bg-green-700 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                \"MCP配置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-theme-border cursor-pointer hover:bg-theme-card-hover group transition-colors duration-200 \".concat((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversation.id ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-theme-primary' : ''),\n                        onClick: ()=>onLoadConversation(conversation.id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-theme-foreground-muted flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-theme-foreground truncate\",\n                                                    children: conversation.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-theme-foreground-muted mt-1\",\n                                            children: [\n                                                conversation.model,\n                                                \" • \",\n                                                new Date(conversation.updated_at).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        onDeleteConversation(conversation.id);\n                                    },\n                                    className: \"p-1 text-theme-foreground-muted hover:text-theme-error opacity-0 group-hover:opacity-100 transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    }, conversation.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/Sidebar.tsx\n"));

/***/ })

});