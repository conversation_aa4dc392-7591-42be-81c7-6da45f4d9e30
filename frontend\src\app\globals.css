@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主题变量定义 */
:root {
  /* 传统变量（兼容现有代码） */
  --background: #ffffff;
  --foreground: #171717;

  /* 新的主题变量系统 - 浅色主题 */
  --color-background: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-tertiary: #f1f5f9;

  --color-foreground: #0f172a;
  --color-foreground-secondary: #334155;
  --color-foreground-muted: #64748b;

  --color-border: #e2e8f0;
  --color-border-secondary: #cbd5e1;

  --color-card: #ffffff;
  --color-card-hover: #f8fafc;

  --color-input: #ffffff;
  --color-input-border: #d1d5db;
  --color-input-focus: #3b82f6;

  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-secondary: #6b7280;
  --color-secondary-hover: #4b5563;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-accent: #8b5cf6;
  --color-accent-hover: #7c3aed;
}

/* 深色主题变量 */
.dark {
  /* 传统变量（兼容现有代码） */
  --background: #0f172a;
  --foreground: #f8fafc;

  /* 新的主题变量系统 - 深色主题 */
  --color-background: #0f172a;
  --color-background-secondary: #1e293b;
  --color-background-tertiary: #334155;

  --color-foreground: #f8fafc;
  --color-foreground-secondary: #e2e8f0;
  --color-foreground-muted: #94a3b8;

  --color-border: #334155;
  --color-border-secondary: #475569;

  --color-card: #1e293b;
  --color-card-hover: #334155;

  --color-input: #1e293b;
  --color-input-border: #475569;
  --color-input-focus: #3b82f6;

  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-secondary: #6b7280;
  --color-secondary-hover: #9ca3af;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-accent: #8b5cf6;
  --color-accent-hover: #7c3aed;
}

/* 系统主题偏好（备用） */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --background: #0f172a;
    --foreground: #f8fafc;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 主题切换过渡效果 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* 防止闪烁的预加载样式 */
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主题切换时的平滑过渡 */
html.theme-changing * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* 自定义滚动条样式 - 支持主题 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-secondary) var(--color-background-tertiary);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--color-background-tertiary);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: var(--color-border-secondary);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-foreground-muted);
}

/* 3D立方体加载动画样式 */
.spinner {
  width: 16px;
  height: 16px;
  animation: spinner-y0fdc1 2s infinite ease;
  transform-style: preserve-3d;
}

/* 小尺寸3D立方体加载动画样式 */
.spinner-small {
  width: 18px;
  height: 18px;
  animation: spinner-y0fdc1 2s infinite ease;
  transform-style: preserve-3d;
}

.spinner > div,
.spinner-small > div {
  background-color: rgba(255, 0, 85, 0.2);
  height: 100%;
  position: absolute;
  width: 100%;
  border: 1px solid #ff3a09;
}

.spinner div:nth-of-type(1) {
  transform: translateZ(-20px) rotateY(180deg);
}

.spinner div:nth-of-type(2) {
  transform: rotateY(-270deg) translateX(50%);
  transform-origin: top right;
}

.spinner div:nth-of-type(3) {
  transform: rotateY(270deg) translateX(-50%);
  transform-origin: center left;
}

.spinner div:nth-of-type(4) {
  transform: rotateX(90deg) translateY(-50%);
  transform-origin: top center;
}

.spinner div:nth-of-type(5) {
  transform: rotateX(-90deg) translateY(50%);
  transform-origin: bottom center;
}

.spinner div:nth-of-type(6) {
  transform: translateZ(20px);
}

/* 小尺寸立方体的变换样式 */
.spinner-small div:nth-of-type(1) {
  transform: translateZ(-12px) rotateY(180deg);
}

.spinner-small div:nth-of-type(2) {
  transform: rotateY(-270deg) translateX(50%);
  transform-origin: top right;
}

.spinner-small div:nth-of-type(3) {
  transform: rotateY(270deg) translateX(-50%);
  transform-origin: center left;
}

.spinner-small div:nth-of-type(4) {
  transform: rotateX(90deg) translateY(-50%);
  transform-origin: top center;
}

.spinner-small div:nth-of-type(5) {
  transform: rotateX(-90deg) translateY(50%);
  transform-origin: bottom center;
}

.spinner-small div:nth-of-type(6) {
  transform: translateZ(12px);
}

@keyframes spinner-y0fdc1 {
  0% {
    transform: rotate(45deg) rotateX(-25deg) rotateY(25deg);
  }

  50% {
    transform: rotate(45deg) rotateX(-385deg) rotateY(25deg);
  }

  100% {
    transform: rotate(45deg) rotateX(-385deg) rotateY(385deg);
  }
}