"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleAlert\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLWFsZXJ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sb0JBQWMsZ0VBQWdCLENBQUMsYUFBZTtJQUNsRDtRQUFDLFFBQVU7UUFBQTtZQUFFLEVBQUk7WUFBTSxDQUFJLFFBQU07WUFBQSxDQUFHO1lBQU0sR0FBSztRQUFBLENBQVU7S0FBQTtJQUN6RDtRQUFDO1FBQVEsQ0FBRTtZQUFBLElBQUksQ0FBTTtZQUFBLElBQUksQ0FBTTtZQUFBLEdBQUksSUFBSztZQUFBLEdBQUksS0FBTTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDakU7UUFBQztRQUFRLENBQUU7WUFBQSxJQUFJLENBQU07WUFBQSxJQUFJLENBQVM7WUFBQSxHQUFJLEtBQU07WUFBQSxHQUFJLEtBQU07WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ3RFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXHNyY1xcaWNvbnNcXGNpcmNsZS1hbGVydC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENpcmNsZUFsZXJ0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThiR2x1WlNCNE1UMGlNVElpSUhneVBTSXhNaUlnZVRFOUlqZ2lJSGt5UFNJeE1pSWdMejRLSUNBOGJHbHVaU0I0TVQwaU1USWlJSGd5UFNJeE1pNHdNU0lnZVRFOUlqRTJJaUI1TWowaU1UWWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaXJjbGUtYWxlcnRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaXJjbGVBbGVydCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NpcmNsZUFsZXJ0JywgW1xuICBbJ2NpcmNsZScsIHsgY3g6ICcxMicsIGN5OiAnMTInLCByOiAnMTAnLCBrZXk6ICcxbWdsYXknIH1dLFxuICBbJ2xpbmUnLCB7IHgxOiAnMTInLCB4MjogJzEyJywgeTE6ICc4JywgeTI6ICcxMicsIGtleTogJzFwa2V1aCcgfV0sXG4gIFsnbGluZScsIHsgeDE6ICcxMicsIHgyOiAnMTIuMDEnLCB5MTogJzE2JywgeTI6ICcxNicsIGtleTogJzRkZnE5MCcgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2lyY2xlQWxlcnQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx":
/*!*****************************************************!*\
  !*** ./src/app/chat/components/ToolCallMessage.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolCallMessage: () => (/* binding */ ToolCallMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction ToolCallMessage(param) {\n    let { toolCall } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    // 实时更新当前时间（仅在执行中时）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToolCallMessage.useEffect\": ()=>{\n            if (toolCall.status === 'executing') {\n                const interval = setInterval({\n                    \"ToolCallMessage.useEffect.interval\": ()=>{\n                        setCurrentTime(Date.now());\n                    }\n                }[\"ToolCallMessage.useEffect.interval\"], 100); // 每100ms更新一次\n                return ({\n                    \"ToolCallMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"ToolCallMessage.useEffect\"];\n            }\n        }\n    }[\"ToolCallMessage.useEffect\"], [\n        toolCall.status\n    ]);\n    const formatExecutionTime = (time)=>{\n        if (!time) return null;\n        return \"\".concat((time / 1000).toFixed(2), \"s\");\n    };\n    // 获取实时执行时间\n    const getRealTimeExecutionTime = ()=>{\n        if (toolCall.status === 'executing') {\n            const elapsed = currentTime - toolCall.startTime;\n            return \"\".concat((elapsed / 1000).toFixed(1), \"s\");\n        }\n        return formatExecutionTime(toolCall.executionTime);\n    };\n    const getStatusColor = ()=>{\n        switch(toolCall.status){\n            case 'executing':\n                return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-300 dark:border-blue-700';\n            case 'completed':\n                return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-300 dark:border-green-700';\n            case 'error':\n                return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 border-red-300 dark:border-red-700';\n            default:\n                return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-700';\n        }\n    };\n    const getStatusText = ()=>{\n        const timeStr = getRealTimeExecutionTime();\n        switch(toolCall.status){\n            case 'executing':\n                return \"执行中... \".concat(timeStr || '');\n            case 'completed':\n                return \"执行完成 \".concat(timeStr || '');\n            case 'error':\n                return \"执行失败 \".concat(timeStr || '');\n            default:\n                return '未知状态';\n        }\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        // 确保text是字符串类型\n        const textStr = typeof text === 'string' ? text : JSON.stringify(text, null, 2);\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const parts = textStr.split(imageUrlRegex);\n        if (parts.length === 1) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: textStr\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 80,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: parts.map((part, index)=>{\n                if (imageUrlRegex.test(part)) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: part,\n                                alt: \"Generated image\",\n                                className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: part\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 15\n                    }, this);\n                }\n                return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: part\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 25\n                }, this) : null;\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    };\n    const renderResult = ()=>{\n        if (toolCall.status === 'executing') {\n            return null;\n        }\n        if (toolCall.status === 'error') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-1\",\n                        children: \"错误信息：\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap\",\n                        children: toolCall.error || '未知错误'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this);\n        }\n        if (toolCall.status === 'completed' && toolCall.result) {\n            // 尝试解析JSON格式的结果\n            let formattedResult = toolCall.result;\n            let isJsonResult = false;\n            try {\n                const parsed = JSON.parse(toolCall.result);\n                formattedResult = JSON.stringify(parsed, null, 2);\n                isJsonResult = true;\n            } catch (e) {\n            // 不是JSON格式，保持原样\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 dark:text-gray-200 mb-1\",\n                        children: \"执行结果：\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto\",\n                        children: isJsonResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap font-mono text-xs bg-black/5 dark:bg-white/5 p-2 rounded\",\n                            children: formattedResult\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: renderImageIfUrl(formattedResult)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-orange-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%] min-w-[300px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                            children: \"工具调用\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg border \".concat(getStatusColor()),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 cursor-pointer hover:bg-black/5 dark:hover:bg-white/5 transition-colors\",\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-4 h-4 text-orange-600 dark:text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: toolCall.toolName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: toolCall.status === 'executing' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-4 h-4 text-orange-600 dark:text-orange-400 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this) : toolCall.status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-600 dark:text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-600 dark:text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 pb-3 border-t border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-75\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"参数：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto\",\n                                                        children: JSON.stringify(toolCall.args, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        renderResult()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolCallMessage, \"VJoPrIBoXpHJEEfIUolZoB21K0E=\");\n_c = ToolCallMessage;\nvar _c;\n$RefreshReg$(_c, \"ToolCallMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx\n"));

/***/ })

});