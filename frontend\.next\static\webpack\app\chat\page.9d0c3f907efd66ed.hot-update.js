"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx":
/*!**************************************************!*\
  !*** ./src/app/chat/components/ThinkingMode.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThinkingMode: () => (/* binding */ ThinkingMode),\n/* harmony export */   hasThinkingContent: () => (/* binding */ hasThinkingContent),\n/* harmony export */   removeThinkingContent: () => (/* binding */ removeThinkingContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ ThinkingMode,hasThinkingContent,removeThinkingContent auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ThinkingMode(param) {\n    let { content, isExpanded, onToggleExpand, defaultHidden = false } = param;\n    _s();\n    const [initiallyHidden, setInitiallyHidden] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultHidden);\n    const [thinkingContent, setThinkingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 提取<think></think>标签内的内容，支持流式渲染\n    const extractThinkingContent = (text)=>{\n        const thinkRegex = /<think>([\\s\\S]*?)(?:<\\/think>|$)/g;\n        const matches = text.match(thinkRegex);\n        if (!matches) return '';\n        return matches.map((match)=>{\n            return match.replace(/<\\/?think>/g, '');\n        }).join('\\n\\n');\n    };\n    // 检测思考状态变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThinkingMode.useEffect\": ()=>{\n            const newExtracted = extractThinkingContent(content);\n            setThinkingContent(newExtracted);\n            const hasThinkStart = /<think>/.test(content);\n            // 如果有思考内容或正在思考，确保组件可见\n            if (newExtracted || hasThinkStart) {\n                if (initiallyHidden) {\n                    setInitiallyHidden(false);\n                    console.log('🔍 检测到思考内容，显示思考面板');\n                }\n            }\n        }\n    }[\"ThinkingMode.useEffect\"], [\n        content,\n        initiallyHidden\n    ]);\n    // 如果初始隐藏，且没有思考内容，则不渲染\n    if (initiallyHidden && !thinkingContent) {\n        return null;\n    }\n    // 检测是否正在思考中（有开始标签但没有结束标签）\n    const isCurrentlyThinking = /<think>/.test(content) && !/<\\/think>/.test(content);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-purple-200 dark:border-purple-700 rounded-lg bg-purple-50 dark:bg-purple-900/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onToggleExpand,\n                className: \"flex items-center justify-between p-3 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-800/20 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-purple-800 dark:text-purple-200\",\n                                children: \"思考模式\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            isCurrentlyThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-3 h-3 text-purple-600 dark:text-purple-400 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1 text-xs text-purple-600 dark:text-purple-400\",\n                        children: isCurrentlyThinking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 border border-purple-500 border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"思考中...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-green-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"✓\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"思考完成\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-purple-200 dark:border-purple-700 p-3\",\n                children: [\n                    isCurrentlyThinking && !thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0ms'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '150ms'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '300ms'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"AI正在深度思考中...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, this),\n                    thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-purple-700 dark:text-purple-300 whitespace-pre-wrap bg-white dark:bg-gray-800 dark:border-purple-600\",\n                        children: thinkingContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(ThinkingMode, \"Sag9ybofXahqIKKGqBsCIUomYeQ=\");\n_c = ThinkingMode;\n// 导出辅助函数供其他组件使用\nconst hasThinkingContent = (content)=>{\n    return /<think>/.test(content);\n};\nconst removeThinkingContent = (content)=>{\n    return content.replace(/<think>[\\s\\S]*?(?:<\\/think>|$)/g, '').trim();\n};\nvar _c;\n$RefreshReg$(_c, \"ThinkingMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx\n"));

/***/ })

});