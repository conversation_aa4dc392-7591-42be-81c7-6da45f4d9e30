"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx":
/*!*****************************************************!*\
  !*** ./src/app/chat/components/ToolCallMessage.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolCallMessage: () => (/* binding */ ToolCallMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction ToolCallMessage(param) {\n    let { toolCall } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    // 实时更新当前时间（仅在执行中时）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToolCallMessage.useEffect\": ()=>{\n            if (toolCall.status === 'executing') {\n                const interval = setInterval({\n                    \"ToolCallMessage.useEffect.interval\": ()=>{\n                        setCurrentTime(Date.now());\n                    }\n                }[\"ToolCallMessage.useEffect.interval\"], 100); // 每100ms更新一次\n                return ({\n                    \"ToolCallMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"ToolCallMessage.useEffect\"];\n            }\n        }\n    }[\"ToolCallMessage.useEffect\"], [\n        toolCall.status\n    ]);\n    const formatExecutionTime = (time)=>{\n        if (!time) return null;\n        return \"\".concat((time / 1000).toFixed(2), \"s\");\n    };\n    // 获取实时执行时间\n    const getRealTimeExecutionTime = ()=>{\n        if (toolCall.status === 'executing') {\n            const elapsed = currentTime - toolCall.startTime;\n            return \"\".concat((elapsed / 1000).toFixed(1), \"s\");\n        }\n        return formatExecutionTime(toolCall.executionTime);\n    };\n    const getStatusColor = ()=>{\n        switch(toolCall.status){\n            case 'executing':\n                return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-300 dark:border-blue-700';\n            case 'completed':\n                return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-300 dark:border-green-700';\n            case 'error':\n                return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 border-red-300 dark:border-red-700';\n            default:\n                return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-700';\n        }\n    };\n    const getStatusText = ()=>{\n        const timeStr = getRealTimeExecutionTime();\n        switch(toolCall.status){\n            case 'executing':\n                return \"执行中... \".concat(timeStr || '');\n            case 'completed':\n                return \"执行完成 \".concat(timeStr || '');\n            case 'error':\n                return \"执行失败 \".concat(timeStr || '');\n            default:\n                return '未知状态';\n        }\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        // 确保text是字符串类型\n        const textStr = typeof text === 'string' ? text : JSON.stringify(text, null, 2);\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const parts = textStr.split(imageUrlRegex);\n        if (parts.length === 1) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: textStr\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 80,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: parts.map((part, index)=>{\n                if (imageUrlRegex.test(part)) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: part,\n                                alt: \"Generated image\",\n                                className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: part\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 15\n                    }, this);\n                }\n                return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: part\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 25\n                }, this) : null;\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    };\n    const renderResult = ()=>{\n        if (toolCall.status === 'executing') {\n            return null;\n        }\n        if (toolCall.status === 'error') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-1\",\n                        children: \"错误信息：\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap\",\n                        children: toolCall.error || '未知错误'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this);\n        }\n        if (toolCall.status === 'completed' && toolCall.result) {\n            // 尝试解析JSON格式的结果\n            let formattedResult = toolCall.result;\n            let isJsonResult = false;\n            try {\n                const parsed = JSON.parse(toolCall.result);\n                formattedResult = JSON.stringify(parsed, null, 2);\n                isJsonResult = true;\n            } catch (e) {\n            // 不是JSON格式，保持原样\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 dark:text-gray-200 mb-1\",\n                        children: \"执行结果：\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto\",\n                        children: isJsonResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap font-mono text-xs bg-black/5 dark:bg-white/5 p-2 rounded\",\n                            children: formattedResult\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: renderImageIfUrl(formattedResult)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-orange-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%] min-w-[300px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                            children: \"工具调用\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg border \".concat(getStatusColor()),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 cursor-pointer hover:bg-black/5 dark:hover:bg-white/5 transition-colors\",\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"\\uD83D\\uDD27 \",\n                                                        toolCall.toolName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                toolCall.status === 'executing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: getRealTimeExecutionTime()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 pb-3 border-t border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-75\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"参数：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto\",\n                                                        children: JSON.stringify(toolCall.args, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        renderResult()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolCallMessage, \"VJoPrIBoXpHJEEfIUolZoB21K0E=\");\n_c = ToolCallMessage;\nvar _c;\n$RefreshReg$(_c, \"ToolCallMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx\n"));

/***/ })

});