"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/moon.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Moon)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Moon = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Moon\", [\n    [\n        \"path\",\n        {\n            d: \"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z\",\n            key: \"a7tn18\"\n        }\n    ]\n]);\n //# sourceMappingURL=moon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbW9vbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLGFBQU8sZ0VBQWdCLENBQUMsTUFBUTtJQUNwQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBc0M7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ3BFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXHNyY1xcaWNvbnNcXG1vb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBNb29uXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVElnTTJFMklEWWdNQ0F3SURBZ09TQTVJRGtnT1NBd0lERWdNUzA1TFRsYUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL21vb25cbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBNb29uID0gY3JlYXRlTHVjaWRlSWNvbignTW9vbicsIFtcbiAgWydwYXRoJywgeyBkOiAnTTEyIDNhNiA2IDAgMCAwIDkgOSA5IDkgMCAxIDEtOS05WicsIGtleTogJ2E3dG4xOCcgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgTW9vbjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/sun.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sun)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Sun = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Sun\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"4\",\n            key: \"4exip2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2v2\",\n            key: \"tus03m\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 20v2\",\n            key: \"1lh1kg\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m4.93 4.93 1.41 1.41\",\n            key: \"149t6j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m17.66 17.66 1.41 1.41\",\n            key: \"ptbguv\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h2\",\n            key: \"1t8f8n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 12h2\",\n            key: \"1q8mjw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6.34 17.66-1.41 1.41\",\n            key: \"1m8zz5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m19.07 4.93-1.41 1.41\",\n            key: \"1shlcs\"\n        }\n    ]\n]);\n //# sourceMappingURL=sun.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/Sidebar.tsx":
/*!*****************************!*\
  !*** ./src/app/Sidebar.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Server,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nfunction Sidebar(param) {\n    let { models, conversations, currentConversation, selectedModel, showModelSelector, onModelChange, onShowModelSelector, onCreateConversation, onLoadConversation, onDeleteConversation } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-theme-card border-r border-theme-border flex flex-col transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-theme-foreground\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                        variant: \"icon\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onShowModelSelector(!showModelSelector),\n                                        className: \"p-2 text-theme-foreground-muted hover:text-theme-foreground transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    showModelSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                children: \"选择模型\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedModel,\n                                onChange: (e)=>onModelChange(e.target.value),\n                                className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\",\n                                children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: model.name,\n                                        children: [\n                                            model.name,\n                                            \" (\",\n                                            (model.size / 1024 / 1024 / 1024).toFixed(1),\n                                            \"GB)\"\n                                        ]\n                                    }, model.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCreateConversation,\n                        disabled: !selectedModel,\n                        className: \"w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            \"新建对话\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/mcp-config\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                \"MCP配置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 group \".concat((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversation.id ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-blue-500' : ''),\n                        onClick: ()=>onLoadConversation(conversation.id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                    children: conversation.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                            children: [\n                                                conversation.model,\n                                                \" • \",\n                                                new Date(conversation.updated_at).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        onDeleteConversation(conversation.id);\n                                    },\n                                    className: \"p-1 text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Server_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    }, conversation.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeSwitch: () => (/* binding */ ThemeSwitch),\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle,ThemeSwitch auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nfunction ThemeToggle(param) {\n    let { className = '', size = 'md', variant = 'button', showLabel = false } = param;\n    _s();\n    const { theme, toggleTheme, isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useThemeToggle)();\n    // 尺寸配置\n    const sizeConfig = {\n        sm: {\n            button: 'w-8 h-8',\n            icon: 'w-4 h-4',\n            text: 'text-xs'\n        },\n        md: {\n            button: 'w-10 h-10',\n            icon: 'w-5 h-5',\n            text: 'text-sm'\n        },\n        lg: {\n            button: 'w-12 h-12',\n            icon: 'w-6 h-6',\n            text: 'text-base'\n        }\n    };\n    const config = sizeConfig[size];\n    if (variant === 'icon') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: toggleTheme,\n            className: \"\\n          \".concat(config.button, \"\\n          flex items-center justify-center\\n          rounded-full\\n          bg-gray-100 hover:bg-gray-200 \\n          dark:bg-gray-800 dark:hover:bg-gray-700\\n          text-gray-600 hover:text-gray-900\\n          dark:text-gray-400 dark:hover:text-gray-100\\n          transition-all duration-200 ease-in-out\\n          transform hover:scale-105 active:scale-95\\n          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\\n          dark:focus:ring-offset-gray-800\\n          \").concat(className, \"\\n        \"),\n            title: isDark ? '切换到浅色模式' : '切换到深色模式',\n            \"aria-label\": isDark ? '切换到浅色模式' : '切换到深色模式',\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"\\n              \".concat(config.icon, \"\\n              absolute inset-0\\n              transition-all duration-300 ease-in-out\\n              \").concat(isDark ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100', \"\\n            \")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"\\n              \".concat(config.icon, \"\\n              absolute inset-0\\n              transition-all duration-300 ease-in-out\\n              \").concat(isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0', \"\\n            \")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"\\n        flex items-center gap-2 px-3 py-2\\n        rounded-lg\\n        bg-gray-100 hover:bg-gray-200 \\n        dark:bg-gray-800 dark:hover:bg-gray-700\\n        text-gray-700 hover:text-gray-900\\n        dark:text-gray-300 dark:hover:text-gray-100\\n        transition-all duration-200 ease-in-out\\n        transform hover:scale-105 active:scale-95\\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\\n        dark:focus:ring-offset-gray-800\\n        \".concat(config.text, \"\\n        \").concat(className, \"\\n      \"),\n        title: isDark ? '切换到浅色模式' : '切换到深色模式',\n        \"aria-label\": isDark ? '切换到浅色模式' : '切换到深色模式',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"\\n            \".concat(config.icon, \"\\n            absolute inset-0\\n            transition-all duration-300 ease-in-out\\n            \").concat(isDark ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100', \"\\n          \")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"\\n            \".concat(config.icon, \"\\n            absolute inset-0\\n            transition-all duration-300 ease-in-out\\n            \").concat(isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0', \"\\n          \")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-medium\",\n                children: isDark ? '浅色' : '深色'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeToggle, \"w0sl5LvvDSII/6tCQC+Gbhhs2aU=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useThemeToggle\n    ];\n});\n_c = ThemeToggle;\n// 简化版主题切换开关\nfunction ThemeSwitch(param) {\n    let { className = '' } = param;\n    _s1();\n    const { isDark, toggleTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useThemeToggle)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"\\n        relative inline-flex h-6 w-11 items-center rounded-full\\n        transition-colors duration-200 ease-in-out\\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\\n        dark:focus:ring-offset-gray-800\\n        \".concat(isDark ? 'bg-blue-600' : 'bg-gray-300', \"\\n        \").concat(className, \"\\n      \"),\n        role: \"switch\",\n        \"aria-checked\": isDark,\n        \"aria-label\": isDark ? '切换到浅色模式' : '切换到深色模式',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"\\n          inline-block h-4 w-4 transform rounded-full bg-white\\n          transition-transform duration-200 ease-in-out\\n          \".concat(isDark ? 'translate-x-6' : 'translate-x-1', \"\\n        \")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s1(ThemeSwitch, \"JxKMBuEHAvNBSE2K0FN+41k/ztw=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useThemeToggle\n    ];\n});\n_c1 = ThemeSwitch;\nvar _c, _c1;\n$RefreshReg$(_c, \"ThemeToggle\");\n$RefreshReg$(_c1, \"ThemeSwitch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeToggle.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(app-pages-browser)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeToggle auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n// 创建主题上下文\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 主题提供者组件\nfunction ThemeProvider(param) {\n    let { children, defaultTheme = 'light' } = param;\n    _s();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 设置主题的函数\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.saveTheme)(newTheme);\n    };\n    // 切换主题的函数\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : 'light';\n        setTheme(newTheme);\n    };\n    // 初始化主题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const initialTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.initializeTheme)();\n            setThemeState(initialTheme);\n            setIsInitialized(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // 监听系统主题变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!isInitialized) return;\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": (e)=>{\n                    // 只有在没有手动设置主题时才跟随系统主题\n                    const savedTheme = localStorage.getItem('theme');\n                    if (!savedTheme) {\n                        const systemTheme = e.matches ? 'dark' : 'light';\n                        setTheme(systemTheme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        isInitialized\n    ]);\n    // 提供主题配置\n    const themeConfig = {\n        theme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: themeConfig,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeProvider, \"KhVdDxx916s6/NYn3mLEvVVlSgc=\");\n_c = ThemeProvider;\n// 使用主题的Hook\nfunction useTheme() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 主题切换Hook（简化版本）\nfunction useThemeToggle() {\n    _s2();\n    const { theme, toggleTheme } = useTheme();\n    return {\n        theme,\n        toggleTheme,\n        isDark: theme === 'dark',\n        isLight: theme === 'light'\n    };\n}\n_s2(useThemeToggle, \"Q4eAjrIZ0CuRuhycs6byifK2KBk=\", false, function() {\n    return [\n        useTheme\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ThemeContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getSavedTheme: () => (/* binding */ getSavedTheme),\n/* harmony export */   getSystemTheme: () => (/* binding */ getSystemTheme),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   saveTheme: () => (/* binding */ saveTheme),\n/* harmony export */   themeColors: () => (/* binding */ themeColors)\n/* harmony export */ });\n// 主题类型定义\n// 主题颜色方案\nconst themeColors = {\n    light: {\n        // 背景色\n        background: '#ffffff',\n        backgroundSecondary: '#f8fafc',\n        backgroundTertiary: '#f1f5f9',\n        // 前景色\n        foreground: '#0f172a',\n        foregroundSecondary: '#334155',\n        foregroundMuted: '#64748b',\n        // 边框色\n        border: '#e2e8f0',\n        borderSecondary: '#cbd5e1',\n        // 卡片背景\n        card: '#ffffff',\n        cardHover: '#f8fafc',\n        // 输入框\n        input: '#ffffff',\n        inputBorder: '#d1d5db',\n        inputFocus: '#3b82f6',\n        // 按钮\n        primary: '#3b82f6',\n        primaryHover: '#2563eb',\n        secondary: '#6b7280',\n        secondaryHover: '#4b5563',\n        // 状态色\n        success: '#10b981',\n        warning: '#f59e0b',\n        error: '#ef4444',\n        info: '#3b82f6',\n        // 特殊色\n        accent: '#8b5cf6',\n        accentHover: '#7c3aed'\n    },\n    dark: {\n        // 背景色\n        background: '#0f172a',\n        backgroundSecondary: '#1e293b',\n        backgroundTertiary: '#334155',\n        // 前景色\n        foreground: '#f8fafc',\n        foregroundSecondary: '#e2e8f0',\n        foregroundMuted: '#94a3b8',\n        // 边框色\n        border: '#334155',\n        borderSecondary: '#475569',\n        // 卡片背景\n        card: '#1e293b',\n        cardHover: '#334155',\n        // 输入框\n        input: '#1e293b',\n        inputBorder: '#475569',\n        inputFocus: '#3b82f6',\n        // 按钮\n        primary: '#3b82f6',\n        primaryHover: '#2563eb',\n        secondary: '#6b7280',\n        secondaryHover: '#9ca3af',\n        // 状态色\n        success: '#10b981',\n        warning: '#f59e0b',\n        error: '#ef4444',\n        info: '#3b82f6',\n        // 特殊色\n        accent: '#8b5cf6',\n        accentHover: '#7c3aed'\n    }\n};\n// 获取系统主题偏好\nconst getSystemTheme = ()=>{\n    if (true) {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    }\n    return 'light';\n};\n// 从localStorage获取保存的主题\nconst getSavedTheme = ()=>{\n    if (true) {\n        const saved = localStorage.getItem('theme');\n        if (saved === 'light' || saved === 'dark') {\n            return saved;\n        }\n    }\n    return null;\n};\n// 保存主题到localStorage\nconst saveTheme = (theme)=>{\n    if (true) {\n        localStorage.setItem('theme', theme);\n    }\n};\n// 应用主题到DOM\nconst applyTheme = (theme)=>{\n    if (true) {\n        const root = document.documentElement;\n        const colors = themeColors[theme];\n        // 移除旧的主题类\n        root.classList.remove('light', 'dark');\n        // 添加新的主题类\n        root.classList.add(theme);\n        // 设置CSS变量\n        Object.entries(colors).forEach((param)=>{\n            let [key, value] = param;\n            root.style.setProperty(\"--color-\".concat(key), value);\n        });\n        // 设置传统的CSS变量（兼容现有代码）\n        root.style.setProperty('--background', colors.background);\n        root.style.setProperty('--foreground', colors.foreground);\n    }\n};\n// 初始化主题\nconst initializeTheme = ()=>{\n    const savedTheme = getSavedTheme();\n    const theme = savedTheme || getSystemTheme();\n    applyTheme(theme);\n    return theme;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/theme.ts\n"));

/***/ })

});