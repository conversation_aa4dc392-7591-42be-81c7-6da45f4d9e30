"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ChatArea.tsx":
/*!**********************************************!*\
  !*** ./src/app/chat/components/ChatArea.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatArea: () => (/* binding */ ChatArea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_chat_components_ChatHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/chat/components/ChatHeader */ \"(app-pages-browser)/./src/app/chat/components/ChatHeader.tsx\");\n/* harmony import */ var _app_chat_components_MessageList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/chat/components/MessageList */ \"(app-pages-browser)/./src/app/chat/components/MessageList.tsx\");\n/* harmony import */ var _app_chat_components_ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/chat/components/ChatInput */ \"(app-pages-browser)/./src/app/chat/components/ChatInput.tsx\");\n/* harmony import */ var _app_chat_components_ErrorDisplay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/chat/components/ErrorDisplay */ \"(app-pages-browser)/./src/app/chat/components/ErrorDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatArea auto */ \n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentConversation, messages, inputMessage, isStreaming, error, selectedModel, enableTools, selectedTools, aiState, activeToolCalls, toolCallMessages, thinkingStartTime, onInputChange, onSendMessage, onStopGeneration, onKeyPress, onToolsToggle, onSelectedToolsChange, onClearChat } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_ChatHeader__WEBPACK_IMPORTED_MODULE_2__.ChatHeader, {\n                currentConversation: currentConversation\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_MessageList__WEBPACK_IMPORTED_MODULE_3__.MessageList, {\n                    messages: messages,\n                    isStreaming: isStreaming,\n                    aiState: aiState,\n                    activeToolCalls: activeToolCalls,\n                    toolCallMessages: toolCallMessages,\n                    thinkingStartTime: thinkingStartTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border p-4 space-y-3 transition-colors duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_ErrorDisplay__WEBPACK_IMPORTED_MODULE_5__.ErrorDisplay, {\n                        error: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_ChatInput__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {\n                        inputMessage: inputMessage,\n                        isStreaming: isStreaming,\n                        selectedModel: selectedModel,\n                        enableTools: enableTools,\n                        selectedTools: selectedTools,\n                        onInputChange: onInputChange,\n                        onSendMessage: onSendMessage,\n                        onStopGeneration: onStopGeneration,\n                        onKeyPress: onKeyPress,\n                        onToolsToggle: onToolsToggle,\n                        onSelectedToolsChange: onSelectedToolsChange,\n                        onClearChat: onClearChat\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ChatArea.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ChatArea.tsx\n"));

/***/ })

});