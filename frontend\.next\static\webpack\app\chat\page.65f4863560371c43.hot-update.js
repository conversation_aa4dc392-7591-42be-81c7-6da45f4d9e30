"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx":
/*!**************************************************!*\
  !*** ./src/app/chat/components/ThinkingMode.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThinkingMode: () => (/* binding */ ThinkingMode),\n/* harmony export */   hasThinkingContent: () => (/* binding */ hasThinkingContent),\n/* harmony export */   removeThinkingContent: () => (/* binding */ removeThinkingContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Check,ChevronDown,ChevronRight,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* __next_internal_client_entry_do_not_use__ ThinkingMode,hasThinkingContent,removeThinkingContent auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ThinkingMode(param) {\n    let { content, isExpanded, onToggleExpand, defaultHidden = false } = param;\n    _s();\n    const [initiallyHidden, setInitiallyHidden] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultHidden);\n    const [thinkingContent, setThinkingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 提取<think></think>标签内的内容，支持流式渲染\n    const extractThinkingContent = (text)=>{\n        const thinkRegex = /<think>([\\s\\S]*?)(?:<\\/think>|$)/g;\n        const matches = text.match(thinkRegex);\n        if (!matches) return '';\n        return matches.map((match)=>{\n            return match.replace(/<\\/?think>/g, '');\n        }).join('\\n\\n');\n    };\n    // 检测思考状态变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThinkingMode.useEffect\": ()=>{\n            const newExtracted = extractThinkingContent(content);\n            setThinkingContent(newExtracted);\n            const hasThinkStart = /<think>/.test(content);\n            // 如果有思考内容或正在思考，确保组件可见\n            if (newExtracted || hasThinkStart) {\n                if (initiallyHidden) {\n                    setInitiallyHidden(false);\n                    console.log('🔍 检测到思考内容，显示思考面板');\n                }\n            }\n        }\n    }[\"ThinkingMode.useEffect\"], [\n        content,\n        initiallyHidden\n    ]);\n    // 如果初始隐藏，且没有思考内容，则不渲染\n    if (initiallyHidden && !thinkingContent) {\n        return null;\n    }\n    // 检测是否正在思考中（有开始标签但没有结束标签）\n    const isCurrentlyThinking = /<think>/.test(content) && !/<\\/think>/.test(content);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-purple-200 dark:border-purple-700 rounded-lg bg-purple-50 dark:bg-purple-900/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onToggleExpand,\n                className: \"flex items-center justify-between p-3 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-800/20 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-purple-800 dark:text-purple-200\",\n                                children: \"思考模式\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            isCurrentlyThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-3 h-3 text-purple-600 dark:text-purple-400 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: isCurrentlyThinking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4 text-purple-600 dark:text-purple-400 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-green-600 dark:text-green-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-purple-200 dark:border-purple-700 p-3\",\n                children: [\n                    isCurrentlyThinking && !thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Check_ChevronDown_ChevronRight_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this),\n                    thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-purple-700 dark:text-purple-300 whitespace-pre-wrap bg-white dark:bg-gray-800 dark:border-purple-600\",\n                        children: thinkingContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(ThinkingMode, \"Sag9ybofXahqIKKGqBsCIUomYeQ=\");\n_c = ThinkingMode;\n// 导出辅助函数供其他组件使用\nconst hasThinkingContent = (content)=>{\n    return /<think>/.test(content);\n};\nconst removeThinkingContent = (content)=>{\n    return content.replace(/<think>[\\s\\S]*?(?:<\\/think>|$)/g, '').trim();\n};\nvar _c;\n$RefreshReg$(_c, \"ThinkingMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx\n"));

/***/ })

});