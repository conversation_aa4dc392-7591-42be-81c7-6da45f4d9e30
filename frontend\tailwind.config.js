/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class', // 启用类名模式的深色主题
  theme: {
    extend: {
      colors: {
        // 传统变量（兼容现有代码）
        background: 'var(--background)',
        foreground: 'var(--foreground)',

        // 新的主题变量系统
        theme: {
          background: 'var(--color-background)',
          'background-secondary': 'var(--color-background-secondary)',
          'background-tertiary': 'var(--color-background-tertiary)',

          foreground: 'var(--color-foreground)',
          'foreground-secondary': 'var(--color-foreground-secondary)',
          'foreground-muted': 'var(--color-foreground-muted)',

          border: 'var(--color-border)',
          'border-secondary': 'var(--color-border-secondary)',

          card: 'var(--color-card)',
          'card-hover': 'var(--color-card-hover)',

          input: 'var(--color-input)',
          'input-border': 'var(--color-input-border)',
          'input-focus': 'var(--color-input-focus)',

          primary: 'var(--color-primary)',
          'primary-hover': 'var(--color-primary-hover)',
          secondary: 'var(--color-secondary)',
          'secondary-hover': 'var(--color-secondary-hover)',

          success: 'var(--color-success)',
          warning: 'var(--color-warning)',
          error: 'var(--color-error)',
          info: 'var(--color-info)',

          accent: 'var(--color-accent)',
          'accent-hover': 'var(--color-accent-hover)',
        },
      },
      animation: {
        'theme-transition': 'theme-transition 0.3s ease-in-out',
      },
      keyframes: {
        'theme-transition': {
          '0%': { opacity: '0.8' },
          '100%': { opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}