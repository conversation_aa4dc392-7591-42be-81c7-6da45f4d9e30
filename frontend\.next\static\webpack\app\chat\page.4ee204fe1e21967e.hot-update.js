"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx":
/*!*****************************************************!*\
  !*** ./src/app/chat/components/ToolCallMessage.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolCallMessage: () => (/* binding */ ToolCallMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,ChevronDown,ChevronRight,Loader2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction ToolCallMessage(param) {\n    let { toolCall } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    // 实时更新当前时间（仅在执行中时）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToolCallMessage.useEffect\": ()=>{\n            if (toolCall.status === 'executing') {\n                const interval = setInterval({\n                    \"ToolCallMessage.useEffect.interval\": ()=>{\n                        setCurrentTime(Date.now());\n                    }\n                }[\"ToolCallMessage.useEffect.interval\"], 100); // 每100ms更新一次\n                return ({\n                    \"ToolCallMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"ToolCallMessage.useEffect\"];\n            }\n        }\n    }[\"ToolCallMessage.useEffect\"], [\n        toolCall.status\n    ]);\n    const formatExecutionTime = (time)=>{\n        if (!time) return null;\n        return \"\".concat((time / 1000).toFixed(2), \"s\");\n    };\n    const getStatusColor = ()=>{\n        switch(toolCall.status){\n            case 'executing':\n                return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-300 dark:border-blue-700';\n            case 'completed':\n                return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-300 dark:border-green-700';\n            case 'error':\n                return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 border-red-300 dark:border-red-700';\n            default:\n                return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-700';\n        }\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        // 确保text是字符串类型\n        const textStr = typeof text === 'string' ? text : JSON.stringify(text, null, 2);\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const parts = textStr.split(imageUrlRegex);\n        if (parts.length === 1) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: textStr\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 61,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: parts.map((part, index)=>{\n                if (imageUrlRegex.test(part)) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: part,\n                                alt: \"Generated image\",\n                                className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: part\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 15\n                    }, this);\n                }\n                return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: part\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 25\n                }, this) : null;\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    };\n    const renderResult = ()=>{\n        if (toolCall.status === 'executing') {\n            return null;\n        }\n        if (toolCall.status === 'error') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-1\",\n                        children: \"错误信息：\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap\",\n                        children: toolCall.error || '未知错误'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this);\n        }\n        if (toolCall.status === 'completed' && toolCall.result) {\n            // 尝试解析JSON格式的结果\n            let formattedResult = toolCall.result;\n            let isJsonResult = false;\n            try {\n                const parsed = JSON.parse(toolCall.result);\n                formattedResult = JSON.stringify(parsed, null, 2);\n                isJsonResult = true;\n            } catch (e) {\n            // 不是JSON格式，保持原样\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-gray-800 dark:text-gray-200 mb-1\",\n                        children: \"执行结果：\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto\",\n                        children: isJsonResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap font-mono text-xs bg-black/5 dark:bg-white/5 p-2 rounded\",\n                            children: formattedResult\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: renderImageIfUrl(formattedResult)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-orange-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%] min-w-[300px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                            children: \"工具调用\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg border \".concat(getStatusColor()),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 cursor-pointer hover:bg-black/5 dark:hover:bg-white/5 transition-colors\",\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-4 h-4 text-orange-600 dark:text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: toolCall.toolName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: toolCall.status === 'executing' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-4 h-4 text-orange-600 dark:text-orange-400 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this) : toolCall.status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-600 dark:text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-600 dark:text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_ChevronDown_ChevronRight_Loader2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 pb-3 border-t border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-75\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"参数：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto\",\n                                                        children: JSON.stringify(toolCall.args, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        renderResult()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallMessage.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolCallMessage, \"VJoPrIBoXpHJEEfIUolZoB21K0E=\");\n_c = ToolCallMessage;\nvar _c;\n$RefreshReg$(_c, \"ToolCallMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx\n"));

/***/ })

});