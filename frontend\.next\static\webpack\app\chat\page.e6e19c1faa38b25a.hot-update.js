"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx":
/*!**************************************************!*\
  !*** ./src/app/chat/components/ThinkingMode.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThinkingMode: () => (/* binding */ ThinkingMode),\n/* harmony export */   hasThinkingContent: () => (/* binding */ hasThinkingContent),\n/* harmony export */   removeThinkingContent: () => (/* binding */ removeThinkingContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* __next_internal_client_entry_do_not_use__ ThinkingMode,hasThinkingContent,removeThinkingContent auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ThinkingMode(param) {\n    let { content, isExpanded, onToggleExpand, defaultHidden = false } = param;\n    _s();\n    const [initiallyHidden, setInitiallyHidden] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultHidden);\n    const [thinkingContent, setThinkingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 提取<think></think>标签内的内容，支持流式渲染\n    const extractThinkingContent = (text)=>{\n        const thinkRegex = /<think>([\\s\\S]*?)(?:<\\/think>|$)/g;\n        const matches = text.match(thinkRegex);\n        if (!matches) return '';\n        return matches.map((match)=>{\n            return match.replace(/<\\/?think>/g, '');\n        }).join('\\n\\n');\n    };\n    // 检测思考状态变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThinkingMode.useEffect\": ()=>{\n            const newExtracted = extractThinkingContent(content);\n            setThinkingContent(newExtracted);\n            const hasThinkStart = /<think>/.test(content);\n            // 如果有思考内容或正在思考，确保组件可见\n            if (newExtracted || hasThinkStart) {\n                if (initiallyHidden) {\n                    setInitiallyHidden(false);\n                    console.log('🔍 检测到思考内容，显示思考面板');\n                }\n            }\n        }\n    }[\"ThinkingMode.useEffect\"], [\n        content,\n        initiallyHidden\n    ]);\n    // 如果初始隐藏，且没有思考内容，则不渲染\n    if (initiallyHidden && !thinkingContent) {\n        return null;\n    }\n    // 检测是否正在思考中（有开始标签但没有结束标签）\n    const isCurrentlyThinking = /<think>/.test(content) && !/<\\/think>/.test(content);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-purple-200 dark:border-purple-700 rounded-lg bg-purple-50 dark:bg-purple-900/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: onToggleExpand,\n                className: \"flex items-center justify-between p-3 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-800/20 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-purple-800 dark:text-purple-200\",\n                                children: \"思考模式\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            isCurrentlyThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 text-xs text-purple-600 dark:text-purple-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"思考中...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1 text-xs text-purple-600 dark:text-purple-400\",\n                        children: isCurrentlyThinking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 border border-purple-500 border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"思考中...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-green-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"✓\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"思考完成\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-purple-200 dark:border-purple-700 p-3\",\n                children: [\n                    isCurrentlyThinking && !thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0ms'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '150ms'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '300ms'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"AI正在深度思考中...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this),\n                    thinkingContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-purple-700 dark:text-purple-300 whitespace-pre-wrap bg-white dark:bg-gray-800 dark:border-purple-600\",\n                        children: thinkingContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ThinkingMode.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(ThinkingMode, \"Sag9ybofXahqIKKGqBsCIUomYeQ=\");\n_c = ThinkingMode;\n// 导出辅助函数供其他组件使用\nconst hasThinkingContent = (content)=>{\n    return /<think>/.test(content);\n};\nconst removeThinkingContent = (content)=>{\n    return content.replace(/<think>[\\s\\S]*?(?:<\\/think>|$)/g, '').trim();\n};\nvar _c;\n$RefreshReg$(_c, \"ThinkingMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx\n"));

/***/ })

});